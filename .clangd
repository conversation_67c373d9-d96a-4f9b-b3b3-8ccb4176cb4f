CompileFlags:
  Add:
    # ARM toolchain standard library paths
    - -ID:/Program_Tools/GNU Arm Embedded Toolchain/10 2021.10/arm-none-eabi/include
    - -ID:/Program_Tools/GNU Arm Embedded Toolchain/10 2021.10/arm-none-eabi/include/c++/10.3.1
    - -ID:/Program_Tools/GNU Arm Embedded Toolchain/10 2021.10/arm-none-eabi/include/c++/10.3.1/arm-none-eabi
    - -ID:/Program_Tools/GNU Arm Embedded Toolchain/10 2021.10/lib/gcc/arm-none-eabi/10.3.1/include
    - -ID:/Program_Tools/GNU Arm Embedded Toolchain/10 2021.10/lib/gcc/arm-none-eabi/10.3.1/include-fixed
    
    # Project specific paths (using relative paths)
    - -ILibraries/STM32F1xx_HAL_Driver/Inc
    - -ILibraries/STM32F1xx_HAL_Driver/Inc/Legacy
    - -ILibraries/CMSIS/Device/ST/STM32F1xx/Include
    - -ILibraries/CMSIS/Include
    - -IUser
    - -IBsp/Inc
    
    # Defines
    - -DUSE_HAL_DRIVER
    - -DSTM32F103xE
    - -D__GNUC__
    
    # Target and compilation flags
    - --target=arm-none-eabi
    - -mcpu=cortex-m3
    - -mthumb
    - -std=c11
    - -fno-ms-extensions
    - -fno-delayed-template-parsing
    - -Wno-unknown-warning-option

Index:
  Background: Build

Diagnostics:
  ClangTidy:
    Remove: "*"
  UnusedIncludes: None
  Suppress:
    - "no_member_named"
    - "unknown_typename"
以下是该通信协议的Markdown格式：

# TW6308 通信协议

## 版本历史
- 版本 1.0 - 2025年7月 - 第一版

---

## 目录
- [TW6308 通信协议](#tw6308-通信协议)
  - [版本历史](#版本历史)
  - [目录](#目录)
  - [1. 时序要求](#1-时序要求)
  - [2. 初始化流程](#2-初始化流程)
    - [2.1 寄存器偏移地址](#21-寄存器偏移地址)
      - [寄存器偏移地址表](#寄存器偏移地址表)
    - [2.2 寄存器写入顺序](#22-寄存器写入顺序)
  - [3. 回读寄存器](#3-回读寄存器)
  - [4. 选择锁定指示信号输出](#4-选择锁定指示信号输出)
  - [5. STC8GK08 单片机生成 SPI 信号](#5-stc8gk08-单片机生成-spi-信号)
    - [5.1 硬件 SPI 接口初始化以及功能设置](#51-硬件-spi-接口初始化以及功能设置)
    - [5.2 上电 RST\_N 引脚复位](#52-上电-rst_n-引脚复位)
    - [5.3 SPI 发送一帧数据](#53-spi-发送一帧数据)
    - [5.4 芯片寄存器初始化](#54-芯片寄存器初始化)

---

## 1. 时序要求
- 芯片片外复位模式应用要求使用复位信号 **RST_N** 和 SPI 信号，SPI 信号包括 **SPI_CSB**（片选信号），**SPI_PDI**（数据输入），**SPI_PDO**（数据输出），以及数据传输标志 **SPI_CSB**。

备注：
	（1）**RST_N**为复位信号，低电平有效。为了确保数据配置正确，在上电之后，发送寄存器配置字之前，可以进行一次复位，复位时间至少为 110 个时钟周期。
	（2）在RST_N拉高之后，持续至少110个时钟周期，SPI_CSB方可拉低开始写数。
	（3）时钟频率SPI_CLK的设定范围为100kHz~15MHz，如果需要提高SPI_CLK时钟频率，请与技术支持联系。
	（4）只有将SPI_CSB拉低，才能向TW6308芯片写入数据。
	（5）普通并行只写模式下，每一帧数据的宽度为32bit。
	（6）为确保数据成功写入芯片，数据帧之间的延时状态下，要求SPI_CLK持续至少4个周期。
	（7）为保证芯片工作，正常工作时，需要将SPI_CSB拉高。

---

## 2. 初始化流程

### 2.1 寄存器偏移地址
- 在AFC 软复位模式下，向芯片写入寄存器控制字（只发送RA0~RA5，RA7~RA11，RA13~RA24，RA26~RA29），最后发送数据帧3’b111 对AFC 进行复位。寄存器每一帧数据为32bit，32bit 的数据格式如下：000+5bit 寄存器偏移地址+24bit 寄存器数据。5bit 的寄存器偏移地址见表格1。

#### 寄存器偏移地址表
| 寄存器名称 | 地址（二进制） |
| ----- | ------- |
| RA0   | 00000   |
| RA1   | 00001   |
| RA2   | 00010   |
| RA3   | 00011   |
| RA4   | 00100   |
| RA5   | 00101   |
| RA7   | 00111   |
| RA8   | 01000   |
| RA9   | 01001   |
| RA10  | 01010   |
| RA11  | 01011   |
| RA13  | 01101   |
| RA14  | 01110   |
| RA15  | 01111   |
| RA16  | 10000   |
| RA17  | 10001   |
| RA18  | 10010   |
| RA19  | 10011   |
| RA20  | 10100   |
| RA21  | 10101   |
| RA22  | 10110   |
| RA26  | 11010   |
| RA27  | 11011   |
| RA28  | 11100   |

### 2.2 寄存器写入顺序
- 在片外复位模式下，芯片上电后，**RST_N** 信号对芯片进行复位，复位完毕后开始向芯片写入寄存器。
- 在AFC 软复位模式下，以参考单端负相输入100MHz，直通，100MHz鉴相，整数模式下B 口输出4GHz 为例进行说明。具体寄存器的写入顺序可以按照如下进行：
	(1) 写RA0 寄存器0x00E03241
	(2) 持续8 个周期SPI_CLK
	(3) 写RA1 寄存器0x01000000
	(4) 持续8 个周期SPI_CLK
	(5) 写RA2 寄存器0x02C08054
	(6) 持续8 个周期SPI_CLK
	(7) 写RA3 寄存器0x030C0028
	(8) 持续8 个周期SPI_CLK
	(9) 写RA4 寄存器0x04000000
	(10) 持续8 个周期SPI_CLK
	(11) 写RA5 寄存器0x050000FF
	(12) 持续8 个周期SPI_CLK
	(13) 写RA7 寄存器0x07000944
	(14) 持续8 个周期SPI_CLK
	(15) 写RA8 寄存器0x08530000
	(16) 持续8 个周期SPI_CLK
	(17) 写RA9 寄存器0x09DF0000
	(18) 持续8 个周期SPI_CLK
	(19) 写RA10 寄存器0x0A000000
	(20) 持续8 个周期SPI_CLK
	(21) 写RA11 寄存器0x0B000000
	(22) 持续8 个周期SPI_CLK
	(23) 写RA13 寄存器0x0D888888
	(24) 持续8 个周期SPI_CLK
	(25) 写RA14 寄存器0x0E888888
	(26) 持续8 个周期SPI_CLK
	(27) 写RA15 寄存器0x0F000434
	(28) 持续8 个周期SPI_CLK
	(29) 写RA16 寄存器0x10000808
	(30) 持续8 个周期SPI_CLK
	(31) 写RA17 寄存器0x1100FFD3
	(32) 持续8 个周期SPI_CLK
	(33) 写RA18 寄存器0x12007BC0
	(34) 持续8 个周期SPI_CLK
	(35) 写RA19 寄存器0x13000000
	(36) 持续8 个周期SPI_CLK
	(37) 写RA20 寄存器0x14000035
	(38) 持续8 个周期SPI_CLK
	(39) 写RA21 寄存器0x150001F4
	(40) 持续8 个周期SPI_CLK
	(41) 写RA22 寄存器0x160CCAAA
	(42) 持续8 个周期SPI_CLK
	(43) 写RA26 寄存器0x1A021188
	(44) 持续8 个周期SPI_CLK
	(45) 写RA27 寄存器0x1B03F444
	(46) 持续8 个周期SPI_CLK
	(47) 写RA28 寄存器0x1C048EF5
	(48) 持续8 个周期SPI_CLK
	(49) 写AFC_RST(1)：0xE0
	(50) 持续8 个周期SPI_CLK
	(51) 延时等待频率正常锁定(2)
	主循环(3)（上电初始化输出点频，运行至51 步骤即可中止程序；更新频率
	跳到52 步骤，进入52~56 步骤主循环。）
	(52) 写频率控制寄存器
	(53) 持续8 个周期SPI_CLK
	(54) 写AFC_RST：0xE0
	(55) 持续8 个周期SPI_CLK
	(56) 延时等待频率正常锁定
	注意：
	(1) AFC_RST 数据帧0xE0，实际可以只写入3’b111。
	(2) 写入数据帧完成后，为确保芯片频率正常锁定，建议延时等待至少20us。
	(3) 上电初始化输出点频正常锁定后，如果想改变输出频点，芯片的其他寄存器配置不变，需要先更新修改分频比的寄存器，再写入AFC_RST即可。

---

## 3. 回读寄存器
- 用户可以读寄存器模式读取寄存器值。读取寄存器的操作步骤：
	(1) 进行回读寄存器之前，需开启回读寄存器功能配置，将寄存器RA18[12:10]配置101，然后向芯片写入。初始化过程中可以将RA18 提前配置完成，然后在初始化过程中向芯片写入，如果初始化过程中没有将RA18 提前配置，可以直接修改RA18 的寄存器配置，单读写入寄存器RA18 覆盖先前的寄存器值。
	(2) 按照读寄存器的帧格式向芯片写入数据帧。
	(3) 当数据帧写入100+5bit 地址+00 共10bit 数据后，芯片的SPI_PDO 引
脚开始输出寄存器值。

---

## 4. 选择锁定指示信号输出
	芯片的LD_OUT 引脚为锁定指示信号的输出引脚。锁定检测有LDVT 和LDPD 共2 种检测模式。与锁定指示信号功能相关的寄存器为RA18，其中RA18[8] LDVT_EN，是LDVT 模式的使能配置位，RA18[3] LDPD_EN，是LDPD模式的使能配置位，RA18[13] LD_OUT_SEL，是锁定指示信号的选择配置位。
	当选择LDVT 模式检测锁定指示时，RA18[8]配置1，RA18[3]配置0，RA18[13]配置1。
	当选择LDPD 模式检测锁定指示时，RA18[8]配置0，RA18[3]配置1，RA18[13]配置0。

---
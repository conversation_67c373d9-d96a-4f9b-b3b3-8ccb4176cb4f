/**
  ******************************************************************************
  * @file       : tw6308.h
  * <AUTHOR> yechen
  * @version    : V1.0.0
  * @brief      : TW6308芯片驱动头文件
  ******************************************************************************
  * @attention
  *
  * None
  *
  ******************************************************************************
  */

#ifndef __TW6308_H
#define __TW6308_H

/* 头文件包含 ----------------------------------------------------------------*/
#include "stm32f1xx_hal.h"
#include "main.h"

/* 宏定义 --------------------------------------------------------------------*/
// SPI时钟频率设置 (100kHz ~ 15MHz)
#define TW6308_SPI_CLK_FREQ     1000000     // 1MHz

// GPIO引脚定义
#define TW6308_RST_PORT         GPIOB
#define TW6308_RST_PIN          GPIO_PIN_0

#define TW6308_CS_PORT          GPIOA
#define TW6308_CS_PIN           GPIO_PIN_4

#define TW6308_CLK_PORT         GPIOA
#define TW6308_CLK_PIN          GPIO_PIN_5

#define TW6308_MISO_PORT        GPIOA
#define TW6308_MISO_PIN         GPIO_PIN_6

#define TW6308_MOSI_PORT        GPIOA
#define TW6308_MOSI_PIN         GPIO_PIN_7

// 控制宏定义
#define TW6308_RST_HIGH()       HAL_GPIO_WritePin(TW6308_RST_PORT, TW6308_RST_PIN, GPIO_PIN_SET)
#define TW6308_RST_LOW()        HAL_GPIO_WritePin(TW6308_RST_PORT, TW6308_RST_PIN, GPIO_PIN_RESET)

#define TW6308_CS_HIGH()        HAL_GPIO_WritePin(TW6308_CS_PORT, TW6308_CS_PIN, GPIO_PIN_SET)
#define TW6308_CS_LOW()         HAL_GPIO_WritePin(TW6308_CS_PORT, TW6308_CS_PIN, GPIO_PIN_RESET)

// TW6308寄存器地址定义
#define TW6308_REG_RA0          0x00
#define TW6308_REG_RA1          0x01
#define TW6308_REG_RA2          0x02
#define TW6308_REG_RA3          0x03
#define TW6308_REG_RA4          0x04
#define TW6308_REG_RA5          0x05
#define TW6308_REG_RA7          0x07
#define TW6308_REG_RA8          0x08
#define TW6308_REG_RA9          0x09
#define TW6308_REG_RA10         0x0A
#define TW6308_REG_RA11         0x0B
#define TW6308_REG_RA13         0x0D
#define TW6308_REG_RA14         0x0E
#define TW6308_REG_RA15         0x0F
#define TW6308_REG_RA16         0x10
#define TW6308_REG_RA17         0x11
#define TW6308_REG_RA18         0x12
#define TW6308_REG_RA19         0x13
#define TW6308_REG_RA20         0x14
#define TW6308_REG_RA21         0x15
#define TW6308_REG_RA22         0x16
#define TW6308_REG_RA26         0x1A
#define TW6308_REG_RA27         0x1B
#define TW6308_REG_RA28         0x1C

// AFC复位命令
#define TW6308_AFC_RST          0xE0

/* 类型定义 ------------------------------------------------------------------*/
// TW6308寄存器结构体
typedef struct {
    uint8_t addr;       // 寄存器地址
    uint32_t data;      // 寄存器数据(24位)
} TW6308_RegTypeDef;

/* 函数声明 ------------------------------------------------------------------*/
void TW6308_GPIO_Init(void);
void TW6308_SPI_Init(void);
void TW6308_Reset(void);
HAL_StatusTypeDef TW6308_WriteRegister(uint8_t reg_addr, uint32_t reg_data);
HAL_StatusTypeDef TW6308_ReadRegister(uint8_t reg_addr, uint32_t *reg_data);
HAL_StatusTypeDef TW6308_Init(void);
HAL_StatusTypeDef TW6308_AFC_Reset(void);
HAL_StatusTypeDef TW6308_UpdateFrequency(uint32_t freq_data);
HAL_StatusTypeDef TW6308_UpdateReg(uint8_t reg_addr, uint32_t reg_data);

#endif /* __TW6308_H */

/*********************************************END OF FILE**********************/

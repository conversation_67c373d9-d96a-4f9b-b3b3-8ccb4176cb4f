/**
  ******************************************************************************
  * @file       : sys.c
  * <AUTHOR> yechen
  * @version	: V1.0.0
  * @brief      : 时钟初始化
  ******************************************************************************
  * @attention
  *
  * None
  *
  ******************************************************************************
  */
#include "sys.h"

/**
  * @brief  系统时钟配置
  * @param  Pll : 倍频数，RCC_PLL_MUL2~RCC_PLL_MUL16
  * @retval 晶振状态：1-外部晶振  0：内部晶振
  */ 
uint8_t SystemClock_Config(uint32_t Pll) {
    HAL_StatusTypeDef State = HAL_OK;
    RCC_OscInitTypeDef RCC_OscInitStruct = {0};
    RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
    uint8_t OSC_State = 1;

    // 初始化RCC时钟，频率为72MHz
    RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE; // 时钟源为HSE
    RCC_OscInitStruct.HSEState = RCC_HSE_ON;                   // 有源晶振旁路HSE内部模块，无源晶振需要配置为 RCC_HSE_ON
    RCC_OscInitStruct.HSEPredivValue = RCC_HSE_PREDIV_DIV1;    // HSE预分频
    RCC_OscInitStruct.HSIState = RCC_HSI_OFF;                  // 关闭HSI时钟
    RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;               // 打开PLL
    RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;       // PLL时钟源选择HSE
    RCC_OscInitStruct.PLL.PLLMUL = Pll;                        // 主PLL倍频因子
    State = HAL_RCC_OscConfig(&RCC_OscInitStruct);

    //如果外部晶振初始化失败则把系统时钟改为内部高速晶振，频率为64MHz
	if(State != HAL_OK) {
		OSC_State = 0;
		RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
		RCC_OscInitStruct.HSIState = RCC_HSI_ON;
		RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
		RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
		RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI_DIV2;
		RCC_OscInitStruct.PLL.PLLMUL = RCC_PLL_MUL16;
		State = HAL_RCC_OscConfig(&RCC_OscInitStruct);
		//内部晶振初始化失败就停止运行
		if(State != HAL_OK) while(1);
	}

	//初始化CPU、AHB和APB总线时钟
	RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK | RCC_CLOCKTYPE_SYSCLK | RCC_CLOCKTYPE_PCLK1 | RCC_CLOCKTYPE_PCLK2;
	RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK; 			//设置系统时钟时钟源为PLL
	RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;					//AHB分频系数为1
	RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;					//APB1分频系数为2
	RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;					//APB2分频系数为1
	State = HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2);	//同时设置FLASH延时周期为2WS，也就是3个CPU周期。
	if(State != HAL_OK) while(1);
	
	//回传晶振状态
	return OSC_State;
}


/*********************************************END OF FILE**********************/


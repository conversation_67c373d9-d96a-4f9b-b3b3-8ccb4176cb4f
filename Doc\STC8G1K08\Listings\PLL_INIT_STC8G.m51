<PERSON><PERSON><PERSON> BANKED LINKER/LOCATER V6.22                                                        07/22/2025  16:48:18  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22, INVOKED BY:
D:\SOFTWARE\KEIL\C51\BIN\BL51.EXE .\Objects\STARTUP.obj, .\Objects\main.obj, .\Objects\init.obj TO .\Objects\PLL_INIT_ST
>> C8G PRINT (.\Listings\PLL_INIT_STC8G.m51) RAMSIZE (256)


MEMORY MODEL: SMALL


INPUT MODULES INCLUDED:
  .\Objects\STARTUP.obj (?C_STARTUP)
  .\Objects\main.obj (MAIN)
  .\Objects\init.obj (INIT)


LINK MAP OF MODULE:  .\Objects\PLL_INIT_STC8G (?C_STARTUP)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            DATA    0008H     0004H     UNIT         _DATA_GROUP_
            IDATA   000CH     0001H     UNIT         ?STACK

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     016DH     UNIT         ?PR?INITFREQ?INIT
            CODE    0170H     0034H     UNIT         ?PR?_WRITEBYTE?INIT
            CODE    01A4H     0027H     UNIT         ?PR?AFC?INIT
            CODE    01CBH     001DH     UNIT         ?PR?MAIN?MAIN
            CODE    01E8H     001BH     UNIT         ?PR?_WRITEBIT?INIT
            CODE    0203H     001AH     UNIT         ?PR?IO_INIT?INIT
            CODE    021DH     0010H     UNIT         ?PR?_CLKTIME?INIT
            CODE    022DH     000CH     UNIT         ?C_C51STARTUP



OVERLAY MAP OF MODULE:   .\Objects\PLL_INIT_STC8G (?C_STARTUP)


SEGMENT                          DATA_GROUP 
  +--> CALLED SEGMENT          START    LENGTH
----------------------------------------------
?C_C51STARTUP                  -----    -----
  +--> ?PR?MAIN?MAIN

?PR?MAIN?MAIN                  -----    -----
  +--> ?PR?IO_INIT?INIT
  +--> ?PR?_CLKTIME?INIT
  +--> ?PR?INITFREQ?INIT
  +--> ?PR?AFC?INIT

?PR?INITFREQ?INIT              -----    -----
  +--> ?PR?_WRITEBIT?INIT
  +--> ?PR?_CLKTIME?INIT

?PR?_WRITEBIT?INIT             0008H    0004H
BL51 BANKED LINKER/LOCATER V6.22                                                      07/22/2025  16:48:18  PAGE 2


  +--> ?PR?_WRITEBYTE?INIT

?PR?_WRITEBYTE?INIT            -----    -----
  +--> ?PR?_CLKTIME?INIT

?PR?AFC?INIT                   -----    -----
  +--> ?PR?_CLKTIME?INIT



SYMBOL TABLE OF MODULE:  .\Objects\PLL_INIT_STC8G (?C_STARTUP)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        ?C_STARTUP
  C:022DH         SEGMENT       ?C_C51STARTUP
  I:000CH         SEGMENT       ?STACK
  C:0000H         PUBLIC        ?C_STARTUP
  D:00E0H         SYMBOL        ACC
  D:00F0H         SYMBOL        B
  D:0083H         SYMBOL        DPH
  D:0082H         SYMBOL        DPL
  N:0000H         SYMBOL        IBPSTACK
  N:0100H         SYMBOL        IBPSTACKTOP
  N:0080H         SYMBOL        IDATALEN
  C:0230H         SYMBOL        IDATALOOP
  N:0000H         SYMBOL        PBPSTACK
  N:0100H         SYMBOL        PBPSTACKTOP
  N:0000H         SYMBOL        PDATALEN
  N:0000H         SYMBOL        PDATASTART
  N:0000H         SYMBOL        PPAGE
  N:0000H         SYMBOL        PPAGEENABLE
  D:00A0H         SYMBOL        PPAGE_SFR
  D:0081H         SYMBOL        SP
  C:022DH         SYMBOL        STARTUP1
  N:0000H         SYMBOL        XBPSTACK
  N:0000H         SYMBOL        XBPSTACKTOP
  N:0000H         SYMBOL        XDATALEN
  N:0000H         SYMBOL        XDATASTART
  C:0000H         LINE#         126
  C:022DH         LINE#         133
  C:022FH         LINE#         134
  C:0230H         LINE#         135
  C:0231H         LINE#         136
  C:0233H         LINE#         185
  C:0236H         LINE#         196
  -------         ENDMOD        ?C_STARTUP

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  D:00C8H         PUBLIC        P5
BL51 BANKED LINKER/LOCATER V6.22                                                      07/22/2025  16:48:18  PAGE 3


  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  D:00D8H         PUBLIC        CCON
  C:01CBH         PUBLIC        main
  B:0090H.2       PUBLIC        P12
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  B:0090H.5       PUBLIC        P15
  B:00C8H.5       PUBLIC        P55
  D:00D0H         PUBLIC        PSW
  -------         PROC          MAIN
  C:01CBH         LINE#         10
  C:01CBH         LINE#         11
  C:01CBH         LINE#         12
  C:01CEH         LINE#         13
  C:01D0H         LINE#         14
  C:01D2H         LINE#         15
  C:01D4H         LINE#         16
  C:01D9H         LINE#         17
  C:01DBH         LINE#         18
  C:01E0H         LINE#         19
  C:01E3H         LINE#         20
  C:01E6H         LINE#         21
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        INIT
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0093H         PUBLIC        P0M1
  D:0092H         PUBLIC        P1M0
  D:0096H         PUBLIC        P2M0
  D:0091H         PUBLIC        P1M1
  D:0080H         PUBLIC        P0
  D:00B2H         PUBLIC        P3M0
  D:0095H         PUBLIC        P2M1
  D:0090H         PUBLIC        P1
  D:00B4H         PUBLIC        P4M0
  D:00B1H         PUBLIC        P3M1
  D:00A0H         PUBLIC        P2
  D:00CAH         PUBLIC        P5M0
  D:00B3H         PUBLIC        P4M1
  D:00B0H         PUBLIC        P3
  D:00C9H         PUBLIC        P5M1
  D:00C0H         PUBLIC        P4
  D:00C8H         PUBLIC        P5
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  D:00D8H         PUBLIC        CCON
  C:0003H         PUBLIC        InitFreq
  B:0090H.2       PUBLIC        P12
  D:0098H         PUBLIC        SCON
  B:0090H.3       PUBLIC        P13
  D:0088H         PUBLIC        TCON
  B:0090H.5       PUBLIC        P15
  C:0170H         PUBLIC        _Writebyte
  C:01A4H         PUBLIC        AFC
  C:01E8H         PUBLIC        _Writebit
BL51 BANKED LINKER/LOCATER V6.22                                                      07/22/2025  16:48:18  PAGE 4


  C:0221H         PUBLIC        _clktime
  C:0203H         PUBLIC        io_init
  D:00D0H         PUBLIC        PSW
  D:0094H         PUBLIC        P0M0
  -------         PROC          IO_INIT
  C:0203H         LINE#         5
  C:0203H         LINE#         6
  C:0206H         LINE#         7
  C:0208H         LINE#         8
  C:020AH         LINE#         9
  C:020CH         LINE#         10
  C:020EH         LINE#         11
  C:0210H         LINE#         12
  C:0212H         LINE#         13
  C:0214H         LINE#         14
  C:0216H         LINE#         15
  C:0218H         LINE#         16
  C:021AH         LINE#         17
  C:021CH         LINE#         18
  -------         ENDPROC       IO_INIT
  C:021DH         SYMBOL        L?0011
  -------         PROC          L?0010
  -------         ENDPROC       L?0010
  C:021DH         SYMBOL        L?0011
  -------         PROC          _CLKTIME
  D:0007H         SYMBOL        clks
  C:0221H         LINE#         20
  C:0221H         LINE#         21
  C:0221H         LINE#         22
  C:0227H         LINE#         23
  C:0227H         LINE#         24
  C:0229H         LINE#         25
  C:022CH         LINE#         26
  -------         ENDPROC       _CLKTIME
  -------         PROC          _WRITEBYTE
  D:0006H         SYMBOL        dat
  C:0170H         LINE#         28
  C:0172H         LINE#         29
  C:0172H         LINE#         30
  C:0177H         LINE#         31
  C:017DH         LINE#         32
  C:0183H         LINE#         33
  C:0189H         LINE#         34
  C:018FH         LINE#         35
  C:0195H         LINE#         36
  C:019BH         LINE#         37
  -------         ENDPROC       _WRITEBYTE
  -------         PROC          _WRITEBIT
  D:0006H         SYMBOL        dat1
  D:0005H         SYMBOL        dat2
  D:0004H         SYMBOL        dat3
  D:000BH         SYMBOL        dat4
  C:01E8H         LINE#         40
  C:01EAH         LINE#         41
  C:01EAH         LINE#         42
  C:01ECH         LINE#         43
  C:01EEH         LINE#         45
BL51 BANKED LINKER/LOCATER V6.22                                                      07/22/2025  16:48:18  PAGE 5


  C:01F1H         LINE#         46
  C:01F6H         LINE#         47
  C:01FBH         LINE#         48
  C:0200H         LINE#         50
  C:0202H         LINE#         51
  -------         ENDPROC       _WRITEBIT
  -------         PROC          INITFREQ
  C:0003H         LINE#         55
  C:0003H         LINE#         56
  C:0003H         LINE#         57
  C:0014H         LINE#         58
  C:0023H         LINE#         59
  C:0034H         LINE#         60
  C:0045H         LINE#         61
  C:0053H         LINE#         62
  C:0060H         LINE#         63
  C:006FH         LINE#         64
  C:007FH         LINE#         65
  C:008EH         LINE#         66
  C:009CH         LINE#         67
  C:00A9H         LINE#         68
  C:00BAH         LINE#         69
  C:00C7H         LINE#         70
  C:00D8H         LINE#         71
  C:00E7H         LINE#         72
  C:00F6H         LINE#         73
  C:0105H         LINE#         74
  C:0113H         LINE#         75
  C:0120H         LINE#         76
  C:012EH         LINE#         77
  C:013FH         LINE#         78
  C:0150H         LINE#         79
  C:0160H         LINE#         80
  -------         ENDPROC       INITFREQ
  -------         PROC          AFC
  C:01A4H         LINE#         84
  C:01A4H         LINE#         85
  C:01A4H         LINE#         86
  C:01A6H         LINE#         87
  C:01A8H         LINE#         89
  C:01AFH         LINE#         90
  C:01B6H         LINE#         91
  C:01BDH         LINE#         92
  C:01C4H         LINE#         93
  C:01C6H         LINE#         94
  -------         ENDPROC       AFC
  -------         ENDMOD        INIT

Program Size: data=13.0 xdata=0 code=569
LINK/LOCATE RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)

# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
This is an STM32F103VET6 microcontroller project template using Keil MDK (ARM-ADS toolchain). The project implements a TW6308 chip control system via SPI communication with UART command interface.

## Build Commands
- **Build Project**: Open `Project\Template.uvprojx` in Keil MDK and press F7 or use Build menu
- **Clean Project**: Use the `keilkilll.bat` script in the root directory
- **Output Files**: Built hex file is located in `Project\Objects\Template.hex`

## Project Structure
- **User/**: Application code (main.c, interrupt handlers, HAL configuration)
- **Bsp/**: Board Support Package - custom drivers including:
  - `tw6308.c/h`: TW6308 chip driver implementation
  - `delay.c/h`: Delay functions
  - `sys.c/h`: System clock configuration
  - `usart.c/h`: UART communication
- **Libraries/**: STM32F1xx HAL drivers and CMSIS files
- **Project/**: Keil project files, build outputs, and debug configurations
- **Doc/**: Documentation including TW6308 communication protocol and STC8G1K08 reference code

## Key Components
- **MCU**: STM32F103VET6 (Cortex-M3, 512KB Flash, 64KB RAM)
- **Clock**: Configurable to run at 72MHz (external crystal) or 64MHz (internal oscillator)
- **Communication**: 
  - UART1 at 115200 baud for command interface
  - SPI1 for TW6308 control
- **Target Device**: TW6308 chip controlled via SPI interface

## TW6308 Driver Implementation

### Pin Configuration
- PA4: SPI1_NSS (Chip Select)
- PA5: SPI1_SCK (SPI Clock)
- PA6: SPI1_MISO (Master In Slave Out)
- PA7: SPI1_MOSI (Master Out Slave In)
- PB0: RST (Reset signal)

### Key Functions
- `TW6308_Init()`: Initializes the chip with default register configuration
- `TW6308_WriteRegister()`: Writes a single register
- `TW6308_ReadRegister()`: Reads a single register
- `TW6308_UpdateReg()`: Updates a register and performs AFC reset
- `TW6308_UpdateFrequency()`: Updates frequency register (RA8) and performs AFC reset
- `TW6308_AFC_Reset()`: Sends AFC reset command (0xE0)

### SPI Configuration
- Mode: Master
- Data Size: 8-bit
- Clock Polarity: Low (CPOL=0)
- Clock Phase: 1st Edge (CPHA=0)
- Baud Rate: ~1.125MHz (72MHz/64)
- MSB First

## UART Command Protocol
The system accepts commands from the manufacturer's PC software via UART:
- **Command Format**: 7 bytes total
  - Bytes 0-2: Header (0x40 0x00 0x20)
  - Byte 3: Register address
  - Bytes 4-6: Register data (24-bit, big-endian)
- **Example Commands**:
  - `40 00 20 00 D0 32 40` → Set RA0 = 0xD03240
  - `40 00 20 01 80 00 00` → Set RA1 = 0x800000
  - `40 00 20 02 A0 80 54` → Set RA2 = 0xA08054

## Important Protocol Information
The TW6308 chip requires specific SPI timing and initialization sequence:
- SPI clock range: 100kHz to 15MHz
- 32-bit data frames with format: 000 + 5-bit register address + 24-bit data
- Reset timing: Minimum 110 clock cycles for RST_N
- AFC reset delay: Minimum 20μs after register update
- See `Doc\通信协议.md` for detailed protocol specifications

## Development Notes
- System automatically detects and switches between external (72MHz) and internal (64MHz) clock sources
- UART reception uses interrupt-based handling with status flag at `USART_RX_STA`
- Printf output is disabled by default to avoid interfering with PC software communication
- The driver implements the full initialization sequence for 4GHz output with 100MHz reference
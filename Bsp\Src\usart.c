/**
  ******************************************************************************
  * @file       : usart.c
  * <AUTHOR> yechen
  * @version	: V1.0.0
  * @brief      : 串口驱动
  ******************************************************************************
  * @attention
  *
  * None
  *
  ******************************************************************************
  */
  
/* 头文件包含 -----------------------------------------------------------------*/
#include "usart.h"

/* 变量定义 -------------------------------------------------------------------*/
uint8_t USART_RX_BUF[USART_REC_LEN]; // 接收缓冲,最大USART_REC_LEN个字节.
uint16_t USART_RX_STA = 0;           // 接收状态标记
UART_HandleTypeDef huart1;           // UART1句柄
DMA_HandleTypeDef hdma_usart1_rx;    // UART1 RX DMA句柄

/**
 * @brief  串口1初始化
 * @param  Bound : 串口波特率
 * @retval 无
 */
void Usart1_Init(uint32_t bound) {
    // UART 初始化设置
    huart1.Instance = USART1;                    // USART1
    huart1.Init.BaudRate = bound;                // 波特率
    huart1.Init.WordLength = UART_WORDLENGTH_8B; // 字长为8位数据格式
    huart1.Init.StopBits = UART_STOPBITS_1;      // 一个停止位
    huart1.Init.Parity = UART_PARITY_NONE;       // 无奇偶校验位
    huart1.Init.HwFlowCtl = UART_HWCONTROL_NONE; // 无硬件流控
    huart1.Init.Mode = UART_MODE_TX_RX;          // 收发模式
    HAL_UART_Init(&huart1);                      // HAL_UART_Init()会使能UART1

    // 开启串口错误中断和空闲中断
    __HAL_UART_ENABLE_IT(&huart1, UART_IT_IDLE);
    __HAL_UART_ENABLE_IT(&huart1, UART_IT_ERR);

    // 串口DMA接收设置
    HAL_UART_Receive_DMA(&huart1, USART_RX_BUF, USART_REC_LEN);
}

/**
 * @brief  串口底层初始化函数
 * @param  huart : 串口句柄
 * @retval 无
 * @note   此函数会被HAL_UART_Init()调用
 */
void HAL_UART_MspInit(UART_HandleTypeDef *uartHandle) {
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    // 如果是串口1，进行串口1 MSP初始化
    if (uartHandle->Instance == USART1) {
        __HAL_RCC_GPIOA_CLK_ENABLE();  // 使能GPIOA时钟
        __HAL_RCC_USART1_CLK_ENABLE(); // 使能USART1时钟
        __HAL_RCC_DMA1_CLK_ENABLE();

        /**USART1 管脚配置
        PA9     ------> USART1_TX
        PA10    ------> USART1_RX
        */
        GPIO_InitStruct.Pin = GPIO_PIN_9;
        GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
        GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
        HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

        GPIO_InitStruct.Pin = GPIO_PIN_10;
        GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
        GPIO_InitStruct.Pull = GPIO_NOPULL;
        HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

        // 串口1接收DMA初始化
        hdma_usart1_rx.Instance = DMA1_Channel5;
        hdma_usart1_rx.Init.Direction = DMA_PERIPH_TO_MEMORY;          // DMA方向:外设到内存
        hdma_usart1_rx.Init.PeriphInc = DMA_PINC_DISABLE;              // 外设地址递增关闭
        hdma_usart1_rx.Init.MemInc = DMA_MINC_ENABLE;                  // 内存地址递增开启
        hdma_usart1_rx.Init.PeriphDataAlignment = DMA_PDATAALIGN_BYTE; // 外设数据位宽为字节
        hdma_usart1_rx.Init.MemDataAlignment = DMA_MDATAALIGN_BYTE;    // 内存数据位宽为字节
        hdma_usart1_rx.Init.Mode = DMA_NORMAL;                         // DMA1模式为正常
        hdma_usart1_rx.Init.Priority = DMA_PRIORITY_LOW;               // DMA优先级为高
        HAL_DMA_Init(&hdma_usart1_rx);

        //__HAL_DMA_ENABLE_IT(&hdma_usart1_rx, DMA_IT_TE);		//开启DMA错误中断

        __HAL_LINKDMA(uartHandle, hdmarx, hdma_usart1_rx);

        // 设置串口中断
        HAL_NVIC_EnableIRQ(USART1_IRQn);
        HAL_NVIC_SetPriority(USART1_IRQn, 2, 3);
    }
}

/**
 * @brief  串口错误回调函数
 * @param  huart : 串口句柄
 * @retval 无
 * @note   此函数会被串口中断函数调用
 */
void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart) {
    if (huart->Instance == USART1) // 如果是串口1
    {
        // 如果是DMA错误中断，会自动清除错误中断标志，同时关闭传输，需要重新启动
        if (huart->ErrorCode & HAL_UART_ERROR_DMA) {
            HAL_UART_Receive_DMA(&huart1, USART_RX_BUF, USART_REC_LEN);
        } else // 串口错误中断  按顺序读取SR、DR寄存器可清除溢出错误、噪音错误、帧错误中断
        {
            __HAL_UART_CLEAR_PEFLAG(huart);
            USART1->SR;
            USART1->DR;
        }
    }
}

/**
 * @brief  串口空闲中断回调函数
 * @param  huart : 串口句柄
 * @retval 无
 * @note   此函数会被串口中断函数调用
 */
void HAL_UART_IdleCallback(UART_HandleTypeDef *huart) {
    if (huart->Instance == USART1) {
        HAL_UART_DMAStop(huart);

        // 获取数据长度,并置位接收完成标志
        USART_RX_STA = USART_REC_LEN - __HAL_DMA_GET_COUNTER(&hdma_usart1_rx) + 0x8000;

        // 重新开始DMA传输
        HAL_UART_Receive_DMA(&huart1, USART_RX_BUF, USART_REC_LEN);
    }
}

/**
 * @brief  串口1中断服务函数
 * @param  无
 * @retval 无
 * @note   使用回调函数接收数据
 */
void USART1_IRQHandler(void) {

    if (__HAL_UART_GET_IT_SOURCE(&huart1, UART_IT_IDLE) != RESET) {
        __HAL_UART_CLEAR_PEFLAG(&huart1); // 清除中断标志
        HAL_UART_IdleCallback(&huart1);   // 调用空闲中断回调函数
    }
    HAL_UART_IRQHandler(&huart1); // 调用HAL库中断处理公用函数
}

/**
 * @brief  重定向fputc函数
 * @param  ch : 写入字符  *f:文件指针
 * @retval 无
 */
int fputc(int ch, FILE *f) {
    while ((USART1->SR & 0X40) == 0);
    
    USART1->DR = (uint8_t)ch;

    return (ch);
}

/**
 * @brief  重定向fgetc函数
 * @param  *f:文件指针
 * @retval 无
 */
int fgetc(FILE *f) {
    int ch;

    ch = USART1->DR;

    return (ch);
}

/*********************************************END OF FILE**********************/


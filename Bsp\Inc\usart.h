#ifndef _USART_H
#define _USART_H

/* 头文件包含 ----------------------------------------------------------------*/
#include "stdio.h"
#include "stm32f1xx_hal.h"

/* 宏定义 --------------------------------------------------------------------*/
#define USART_REC_LEN 100 // 定义最大接收字节数 100

/* 全局变量声明 --------------------------------------------------------------*/
extern uint8_t USART_RX_BUF[USART_REC_LEN]; // 接收缓冲
extern uint16_t USART_RX_STA;               // 接收状态标记
extern UART_HandleTypeDef huart1;           // UART句柄

/* 函数声明 ------------------------------------------------------------------*/
void Usart1_Init(uint32_t bound);

#endif

/*********************************************END OF FILE**********************/

/**
  ******************************************************************************
  * @file       : tw6308.c
  * <AUTHOR> yechen
  * @version    : V1.0.0
  * @brief      : TW6308芯片驱动源文件
  ******************************************************************************
  * @attention
  *
  * None
  *
  ******************************************************************************
  */

/* 头文件包含 ----------------------------------------------------------------*/
#include "tw6308.h"
#include "delay.h"

/* 私有变量 ------------------------------------------------------------------*/
static SPI_HandleTypeDef hspi1;

/* 私有函数声明 --------------------------------------------------------------*/
static void TW6308_SPI_WriteByte(uint8_t byte);
static uint8_t TW6308_SPI_ReadByte(void);
static void TW6308_Delay_Clocks(uint16_t clocks);

/**
 * @brief  TW6308 GPIO初始化
 * @param  无
 * @retval 无
 */
void TW6308_GPIO_Init(void) {
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 使能GPIO时钟
    __HAL_RCC_GPIOA_CLK_ENABLE();
    __HAL_RCC_GPIOB_CLK_ENABLE();
    
    // 配置RST引脚
    GPIO_InitStruct.Pin = TW6308_RST_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(TW6308_RST_PORT, &GPIO_InitStruct);
    
    // 配置CS引脚
    GPIO_InitStruct.Pin = TW6308_CS_PIN;
    HAL_GPIO_Init(TW6308_CS_PORT, &GPIO_InitStruct);
    
    // 初始化引脚状态
    TW6308_RST_HIGH();
    TW6308_CS_HIGH();
}

/**
 * @brief  TW6308 SPI初始化
 * @param  无
 * @retval 无
 */
void TW6308_SPI_Init(void) {
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 使能SPI1时钟
    __HAL_RCC_SPI1_CLK_ENABLE();
    
    // SPI1 GPIO配置
    // PA4 ------> SPI1_NSS
    // PA5 ------> SPI1_SCK
    // PA6 ------> SPI1_MISO
    // PA7 ------> SPI1_MOSI
    GPIO_InitStruct.Pin = TW6308_CLK_PIN | TW6308_MOSI_PIN | TW6308_MISO_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
    
    // MISO引脚需要配置为浮空输入
    GPIO_InitStruct.Pin = TW6308_MISO_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_INPUT;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(TW6308_MISO_PORT, &GPIO_InitStruct);
    
    // SPI1参数配置
    hspi1.Instance = SPI1;
    hspi1.Init.Mode = SPI_MODE_MASTER;
    hspi1.Init.Direction = SPI_DIRECTION_2LINES;
    hspi1.Init.DataSize = SPI_DATASIZE_8BIT;
    hspi1.Init.CLKPolarity = SPI_POLARITY_LOW;       // CPOL = 0
    hspi1.Init.CLKPhase = SPI_PHASE_1EDGE;           // CPHA = 0
    hspi1.Init.NSS = SPI_NSS_SOFT;
    hspi1.Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_64;  // 72MHz/64 = 1.125MHz
    hspi1.Init.FirstBit = SPI_FIRSTBIT_MSB;
    hspi1.Init.TIMode = SPI_TIMODE_DISABLE;
    hspi1.Init.CRCCalculation = SPI_CRCCALCULATION_DISABLE;
    hspi1.Init.CRCPolynomial = 10;
    
    if (HAL_SPI_Init(&hspi1) != HAL_OK) {
        // 初始化错误处理
        while(1);
    }
}

/**
 * @brief  TW6308复位
 * @param  无
 * @retval 无
 */
void TW6308_Reset(void) {
    // RST_N拉低复位，至少110个时钟周期
    TW6308_RST_LOW();
    TW6308_Delay_Clocks(220);
    
    // RST_N拉高
    TW6308_RST_HIGH();
    TW6308_Delay_Clocks(220);
}

/**
 * @brief  写入TW6308寄存器
 * @param  reg_addr: 寄存器地址(5位)
 * @param  reg_data: 寄存器数据(24位)
 * @retval HAL状态
 */
HAL_StatusTypeDef TW6308_WriteRegister(uint8_t reg_addr, uint32_t reg_data) {
    uint8_t data[4];
    
    // 组装32位数据: 000 + 5位地址 + 24位数据
    data[0] = (reg_addr & 0x1F);                    // 高字节: 000 + 5位地址
    data[1] = (uint8_t)((reg_data >> 16) & 0xFF);   // 数据高字节
    data[2] = (uint8_t)((reg_data >> 8) & 0xFF);    // 数据中字节
    data[3] = (uint8_t)(reg_data & 0xFF);           // 数据低字节
    
    // 片选拉低
    TW6308_CS_LOW();
    
    // 发送4字节数据
    for(int i = 0; i < 4; i++) {
        TW6308_SPI_WriteByte(data[i]);
    }
    
    // 片选拉高
    TW6308_CS_HIGH();
    
    // 数据帧之间延时至少4个时钟周期
    TW6308_Delay_Clocks(8);
    
    return HAL_OK;
}

/**
 * @brief  读取TW6308寄存器
 * @param  reg_addr: 寄存器地址(5位)
 * @param  reg_data: 寄存器数据指针
 * @retval HAL状态
 */
HAL_StatusTypeDef TW6308_ReadRegister(uint8_t reg_addr, uint32_t *reg_data) {
    uint8_t cmd;
    uint8_t data[3] = {0};
    
    // 组装读命令: 100 + 5位地址
    cmd = 0x80 | (reg_addr & 0x1F);
    
    // 片选拉低
    TW6308_CS_LOW();
    
    // 发送读命令
    TW6308_SPI_WriteByte(cmd);
    TW6308_SPI_WriteByte(0x00);  // 发送2个dummy字节
    
    // 读取3字节数据
    data[0] = TW6308_SPI_ReadByte();
    data[1] = TW6308_SPI_ReadByte();
    data[2] = TW6308_SPI_ReadByte();
    
    // 片选拉高
    TW6308_CS_HIGH();
    
    // 组装24位数据
    *reg_data = ((uint32_t)data[0] << 16) | ((uint32_t)data[1] << 8) | data[2];
    
    return HAL_OK;
}

/**
 * @brief  TW6308芯片初始化
 * @param  无
 * @retval HAL状态
 */
HAL_StatusTypeDef TW6308_Init(void) {
    // GPIO和SPI初始化
    TW6308_GPIO_Init();
    TW6308_SPI_Init();
    
    // 芯片复位
    TW6308_Reset();
    
    // 按照协议要求的顺序写入寄存器配置
    // 参考100MHz，直通，100MHz鉴相，整数模式下B口输出4GHz
    TW6308_WriteRegister(TW6308_REG_RA0,  0xE03241);
    TW6308_WriteRegister(TW6308_REG_RA1,  0x000000);
    TW6308_WriteRegister(TW6308_REG_RA2,  0xC08054);
    TW6308_WriteRegister(TW6308_REG_RA3,  0x0C0028);
    TW6308_WriteRegister(TW6308_REG_RA4,  0x000000);
    TW6308_WriteRegister(TW6308_REG_RA5,  0x0000FF);
    TW6308_WriteRegister(TW6308_REG_RA7,  0x000944);
    TW6308_WriteRegister(TW6308_REG_RA8,  0x530000);
    TW6308_WriteRegister(TW6308_REG_RA9,  0xDF0000);
    TW6308_WriteRegister(TW6308_REG_RA10, 0x000000);
    TW6308_WriteRegister(TW6308_REG_RA11, 0x000000);
    TW6308_WriteRegister(TW6308_REG_RA13, 0x888888);
    TW6308_WriteRegister(TW6308_REG_RA14, 0x888888);
    TW6308_WriteRegister(TW6308_REG_RA15, 0x000434);
    TW6308_WriteRegister(TW6308_REG_RA16, 0x000808);
    TW6308_WriteRegister(TW6308_REG_RA17, 0x00FFD3);
    TW6308_WriteRegister(TW6308_REG_RA18, 0x007BC0);
    TW6308_WriteRegister(TW6308_REG_RA19, 0x000000);
    TW6308_WriteRegister(TW6308_REG_RA20, 0x000035);
    TW6308_WriteRegister(TW6308_REG_RA21, 0x0001F4);
    TW6308_WriteRegister(TW6308_REG_RA22, 0x0CCAAA);
    TW6308_WriteRegister(TW6308_REG_RA26, 0x021188);
    TW6308_WriteRegister(TW6308_REG_RA27, 0x03F444);
    TW6308_WriteRegister(TW6308_REG_RA28, 0x048EF5);
    
    // 发送AFC复位命令
    TW6308_AFC_Reset();
    
    // 延时等待频率正常锁定(至少20us)
    delay_us(50);
    
    return HAL_OK;
}

/**
 * @brief  TW6308 AFC复位
 * @param  无
 * @retval HAL状态
 */
HAL_StatusTypeDef TW6308_AFC_Reset(void) {
    // 片选拉低
    TW6308_CS_LOW();
    
    // 发送AFC复位命令(只需要发送3位111)
    TW6308_SPI_WriteByte(0xE0);
    
    // 片选拉高
    TW6308_CS_HIGH();
    
    // 数据帧之间延时
    TW6308_Delay_Clocks(8);
    
    return HAL_OK;
}

/**
 * @brief  更新TW6308频率
 * @param  freq_data: 频率控制寄存器数据
 * @retval HAL状态
 */
HAL_StatusTypeDef TW6308_UpdateFrequency(uint32_t freq_data) {
    // 写入频率控制寄存器
    // 具体使用哪个寄存器需要根据实际应用确定
    // 这里假设使用RA8寄存器控制频率
    TW6308_WriteRegister(TW6308_REG_RA8, freq_data);
    
    // 发送AFC复位命令
    TW6308_AFC_Reset();
    
    // 延时等待频率正常锁定
    delay_us(50);
    
    return HAL_OK;
}

/**
 * @brief  更新TW6308寄存器
 * @param  reg_addr: 寄存器地址(5位)
 * @param  reg_data: 寄存器数据(24位)
 * @retval HAL状态
 */
HAL_StatusTypeDef TW6308_UpdateReg(uint8_t reg_addr, uint32_t reg_data) {
    // 写入指定的寄存器
    TW6308_WriteRegister(reg_addr, reg_data);
    
    // 发送AFC复位命令
    TW6308_AFC_Reset();
    
    // 延时等待频率正常锁定
    delay_us(50);
    
    return HAL_OK;
}

/**
 * @brief  SPI写入一个字节
 * @param  byte: 要写入的字节
 * @retval 无
 */
static void TW6308_SPI_WriteByte(uint8_t byte) {
    HAL_SPI_Transmit(&hspi1, &byte, 1, 100);
}

/**
 * @brief  SPI读取一个字节
 * @param  无
 * @retval 读取的字节
 */
static uint8_t TW6308_SPI_ReadByte(void) {
    uint8_t byte = 0;
    HAL_SPI_Receive(&hspi1, &byte, 1, 100);
    return byte;
}

/**
 * @brief  延时指定的时钟周期数
 * @param  clocks: 时钟周期数
 * @retval 无
 */
static void TW6308_Delay_Clocks(uint16_t clocks) {
    // 根据SPI时钟频率计算延时
    // 1MHz SPI时钟，每个时钟周期为1us
    uint16_t delay = clocks;
    while(delay--) {
        __NOP();
        __NOP();
        __NOP();
        __NOP();
    }
}

/*********************************************END OF FILE**********************/

/**
  ******************************************************************************
  * @file       : main.c
  * <AUTHOR> yechen
  * @version	: V1.0.0
  * @brief      : 程序主体
  ******************************************************************************
  * @attention
  *
  * None
  *
  ******************************************************************************
  */

/* 头文件包含 ----------------------------------------------------------------*/
#include "main.h"

/* 变量定义 -------------------------------------------------------------------*/
uint8_t MCU_OSC;

/**
 * @brief  主函数
 * @param  无
 * @retval 无
 */
int main(void) {
    HAL_Init();                                 // HAL库初始化
    MCU_OSC = SystemClock_Config(RCC_PLL_MUL9); // 内部参考时64M主频，外部参考时：主频=晶振*传入参数
    MCU_OSC ? delay_init(72) : delay_init(64);  // 根据不同时钟主频初始化延时函数

    Usart1_Init(115200); // 串口初始化
    
    // TW6308芯片初始化
    // printf("TW6308 Initializing...\r\n");
    if (TW6308_Init() == HAL_OK) {
        // printf("TW6308 Init Success!\r\n");
    } else {
        // printf("TW6308 Init Failed!\r\n");
    }

    while (1) {
        if (USART_RX_STA & 0x8000) {
            // 串口接收到数据
            uint16_t len = USART_RX_STA & 0x3FFF;
            
            // 检查是否为厂家上位机命令格式（7字节）
            if (len == 7 && USART_RX_BUF[0] == 0x40 && USART_RX_BUF[1] == 0x00 && USART_RX_BUF[2] == 0x20) {
                // 提取寄存器地址（第4字节）
                uint8_t reg_addr = USART_RX_BUF[3];
                
                // 提取寄存器数据（第5、6、7字节）
                uint32_t reg_data = ((uint32_t)USART_RX_BUF[4] << 16) | 
                                   ((uint32_t)USART_RX_BUF[5] << 8) | 
                                   USART_RX_BUF[6];
                
                // 打印接收到的命令信息
                // printf("Received: RA%d = 0x%06X\r\n", reg_addr, reg_data);
                
                // 更新TW6308寄存器
                if (TW6308_UpdateReg(reg_addr, reg_data) == HAL_OK) {
                    // printf("Update Success!\r\n");
                } else {
                    // printf("Update Failed!\r\n");
                }
            } else {
                // 其他格式的数据处理
                // printf("Unknown command: ");
                // for (int i = 0; i < len; i++) {
                //     printf("%02X ", USART_RX_BUF[i]);
                // }
                // printf("\r\n");
            }
            
            // 清除接收标志
            USART_RX_STA = 0;
        }
    }
}

/*********************************************END OF FILE**********************/
